# 核心规则：优先忽略整个目录（最简洁且高效）
POST_Raw_Version_LiGong/.vs/              
POST_Raw_Version_LiGong/tlh_ins632332_20231121/
POST_Raw_Version_LiGong/x64/
POST_Raw_Version_LiGong/x64/Debug/
POST_Raw_Version_LiGong/Debug/


# 通用编译产物（覆盖所有子目录）
**/*.exe
**/*.dll
**/*.lib
**/*.obj
**/*.pdb
**/*.ilk
**/*.exp
**/*.idb
**/*.tlog
**/*.log
**/*.recipe
**/*.lastbuildstate

# VS项目相关文件
**/*.user
**/*.suo
**/*.sdf
**/*.opensdf
**/*.vcxproj.user
**/*.vcxproj.filters.user
**/*.VC.db
**/*.VC.VC.opendb
**/*.vsidx
**/*.ipch
**/DocumentLayout.json
**/DocumentLayout.backup.json

# 项目特定输出
**/data/inavresult*.txt
**/data/inavdata*.csv
**/data/inavstandard*.csv

# 通用忽略
*.o
*.a
*.so
.DS_Store
Thumbs.db
desktop.ini
*.asv
*.m~
logs/
