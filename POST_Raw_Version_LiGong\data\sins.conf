﻿#SINS配置文件
#*******************************device parameters******************************************
#IMU选择 0:mems 1:ifog
imuSelect=0
#0: imu460/dy数据仿真 1:scha63x 2:ADI16465 3:EPSON-G370
memsType=1
#********************************************************************************

#*******************************installation parameters******************************************
#载体坐标系下矢量，3
#gnssArmLength=0.8221,0.34835,1.1929
gnssArmLength=0.5,0.53,1.25
#双天线相对IMU的安装角度，粗略估计
#gnssAtt_from_vehicle=0,0,-92.3
gnssAtt_from_vehicle=0.0,0.0,-90
#车辆转动引起的牵连运动，暂时未用到
OD_ArmLength=0.0000,0.0000,0.0000
#车底盘相对IMU的安装角度，只是有俯仰和横滚，航向需要标定计算
#OD_Att_from_vehicle=-1.29,-0.02,0
OD_Att_from_vehicle=0.0000,0.0000,0.0000
#********************************************************************************

#*******************************Adjust parameters******************************************
#设备是否需要进行标定，0：需要标定；2：表示无需标定
Adj_Nav_Standard_flag=2
#陀螺初始零偏，rad/s，转台标定获取
#Adj_gyro_off=0.0,0.0,0.0003636
#test
Adj_gyro_off=0.018815,0.023719,-0.42185
# Adj_gyro_off=0.0,0.0,-0.0
#加计初始零偏,单位m/s2，转台标定获取

# Adj_acc_off=0.0,0.0,-0.0
#test
Adj_acc_off=0.116319,0.00642,-0.03551
#天线安装角精准误差，单位度
ADJ_gnssAtt_from_2vehicle=0,0,-0.5216
#IMU与载体的y轴夹角
# //test
ADJ_att_ods2b_filter_deg=-0.8588,-0.57222,-2.605790
# ADJ_att_ods2b_filter_deg=0,0,0
#**********************************************************************************

#********************************Algorithm parameters*****************************************
#采用算法:0:全程kalman滤波；1：ODS模式采用航位推算
methodType=0
#仅仅航位推算，IMU补偿，度/h;目前#wb_set=0,0,8.15;隧道#wb_set=0,0,-6.0;地下车库#wb_set=0,0,80.0
wb_set=0,0,0
#是否启动高精度计算,0:不启动；1：启动
HP=1
#********************************************************************************

#*******************************Deubg parameters******************************************
#仿真or正常运行,参数1为：0表示正常运行1表示仿真
simulate=0
#仿真失锁所在历元数204237，270000
lostepoch=25000
#*******************************************************************************







