/***********************************************************************************
nav cli module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-17          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#ifndef _CRT_SECURE_NO_WARNINGS
#define _CRT_SECURE_NO_WARNINGS
#endif


#include "nav_includes.h"
#include <string.h>
#include <stdio.h>
#ifndef WIN32
//#include "UartAdapter.h"
#include "computerFrameParse.h"
//extern AppSettingTypeDef hSetting;
extern struct calib_t caliData;

#endif


extern unsigned long int g_NavIndex;

void Arm_SendMsg(char* buffer,unsigned short len)
{
#ifndef WIN32
	//Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, (unsigned char *)buffer);
#endif	 
}

void CliShowBuildVersion()
{ 
	char buf[128]={0};
	unsigned short len;
	strcat(buf,SINS_ALGORITHM_BUILD_VERSION); 
	strcat(buf,"@"); 
	strcat(buf,__TIME__); 
	strcat(buf,"_"); 
	strcat(buf,__DATE__);
	len=(unsigned short)strlen(buf);
#ifdef WIN32	
	printf("I-NAV_ALGO@Version:%s.\r\r\n", buf);
#else
	Arm_SendMsg(buf,len);
#endif
}